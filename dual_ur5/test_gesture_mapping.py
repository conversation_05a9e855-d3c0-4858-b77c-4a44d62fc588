#!/usr/bin/env python3
"""
手势映射测试脚本
用于测试和验证基于手势识别的非线性重定向系统
"""

import rclpy
from rclpy.node import Node
from my_interfaces.msg import SetJointAngles
import time
import numpy as np

class GestureMappingTestNode(Node):
    def __init__(self):
        super().__init__('gesture_mapping_test')
        
        # 订阅Paxini手部命令话题
        self.subscription = self.create_subscription(
            SetJointAngles,
            '/dexh13_command',
            self.analyze_gesture_mapping,
            10
        )
        
        self.get_logger().info("🎯 手势映射测试节点启动")
        
        print("\n" + "="*80)
        print("🎯 基于手势识别的非线性重定向测试")
        print("="*80)
        print("📋 请按顺序在VR中做以下手势，观察机械手响应：")
        print()
        print("1. 🖐️  张开手掌 - 期望：机械手完全张开")
        print("2. ✊  握拳 - 期望：机械手握拳")
        print("3. 🤏  捏取手势（拇指+食指）- 期望：精确捏取")
        print("4. 🎯  三指夹取（拇指+食指+中指）- 期望：三指协调夹取")
        print("5. ☝️  指向手势（食指伸直）- 期望：食指伸直其他弯曲")
        print("6. 👌  OK手势 - 期望：拇指食指圆圈，其他伸直")
        print("="*80)
        
        self.frame_count = 0
        self.last_print_time = time.time()
        self.gesture_stats = {}
        
    def analyze_gesture_mapping(self, msg):
        """分析手势映射效果"""
        self.frame_count += 1
        current_time = time.time()
        
        # 每1秒分析一次
        if current_time - self.last_print_time >= 1.0:
            self.last_print_time = current_time
            
            angles = msg.joint_angles
            
            if len(angles) >= 16:
                self.analyze_robot_gesture(angles)
                
    def analyze_robot_gesture(self, angles):
        """分析机械手当前手势"""
        print(f"\n📊 第 {self.frame_count} 帧 - 手势映射分析:")
        print("-" * 60)
        
        # 解析关节角度 (16个角度：每根手指4个关节)
        index_angles = angles[0:4]    # 食指
        middle_angles = angles[4:8]   # 中指
        ring_angles = angles[8:12]    # 无名指
        thumb_angles = angles[12:16]  # 拇指
        
        # 计算主要屈曲角度（忽略侧摆）
        index_flex = (index_angles[1] + index_angles[2]) / 2
        middle_flex = (middle_angles[1] + middle_angles[2]) / 2
        ring_flex = (ring_angles[1] + ring_angles[2]) / 2
        thumb_flex = (thumb_angles[2] + thumb_angles[3]) / 2  # MCP + IP
        
        # 显示详细角度
        print(f"🔸 食指屈曲: {index_flex:5.1f}° {self.get_flex_status(index_flex)}")
        print(f"🔸 中指屈曲: {middle_flex:5.1f}° {self.get_flex_status(middle_flex)}")
        print(f"🔸 无名指屈曲: {ring_flex:5.1f}° {self.get_flex_status(ring_flex)}")
        print(f"🔸 拇指屈曲: {thumb_flex:5.1f}° {self.get_flex_status(thumb_flex)}")
        
        # 识别机械手当前手势
        robot_gesture = self.classify_robot_gesture(index_flex, middle_flex, ring_flex, thumb_flex)
        print(f"🤖 机械手手势: {robot_gesture}")
        
        # 统计手势频率
        if robot_gesture not in self.gesture_stats:
            self.gesture_stats[robot_gesture] = 0
        self.gesture_stats[robot_gesture] += 1
        
        # 显示手势统计
        if self.frame_count % 5 == 0:  # 每5秒显示一次统计
            print(f"📈 手势统计: {dict(sorted(self.gesture_stats.items(), key=lambda x: x[1], reverse=True))}")
            
    def get_flex_status(self, angle):
        """获取屈曲状态"""
        if angle < 20:
            return "🟢伸直"
        elif angle < 50:
            return "🟡半弯"
        else:
            return "🔴弯曲"
            
    def classify_robot_gesture(self, index_flex, middle_flex, ring_flex, thumb_flex):
        """分类机械手手势"""
        avg_flex = (index_flex + middle_flex + ring_flex) / 3
        
        # 手势分类逻辑
        if avg_flex < 25 and thumb_flex < 30:
            return "🖐️ 张开手掌"
        elif avg_flex > 65 and thumb_flex > 50:
            return "✊ 握拳"
        elif index_flex < 30 and middle_flex > 60 and ring_flex > 60:
            return "☝️ 指向手势"
        elif (index_flex < 40 and middle_flex < 40 and ring_flex > 60 and 
              thumb_flex > 30 and thumb_flex < 60):
            return "🎯 三指夹取"
        elif (index_flex > 30 and index_flex < 60 and thumb_flex > 30 and 
              middle_flex < 30 and ring_flex < 30):
            return "👌 OK手势"
        elif (index_flex < 50 and thumb_flex > 40 and 
              middle_flex > 60 and ring_flex > 60):
            return "🤏 捏取手势"
        else:
            return "❓ 未知手势"
    
    def print_mapping_quality_report(self):
        """打印映射质量报告"""
        print("\n" + "="*80)
        print("📊 手势映射质量报告")
        print("="*80)
        
        total_frames = sum(self.gesture_stats.values())
        
        print(f"📈 总帧数: {total_frames}")
        print(f"📋 识别到的手势类型: {len(self.gesture_stats)}")
        
        for gesture, count in sorted(self.gesture_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_frames) * 100
            print(f"   {gesture}: {count} 帧 ({percentage:.1f}%)")
            
        print("\n💡 映射质量评估:")
        if "🎯 三指夹取" in self.gesture_stats:
            print("✅ 三指夹取手势已识别 - 非线性映射工作正常")
        else:
            print("⚠️  未检测到三指夹取手势 - 可能需要调整映射参数")
            
        if "🤏 捏取手势" in self.gesture_stats:
            print("✅ 捏取手势已识别 - 精细动作映射正常")
        else:
            print("⚠️  未检测到捏取手势 - 可能需要调整阈值")

def main():
    rclpy.init()
    
    test_node = GestureMappingTestNode()
    
    try:
        print("\n🚀 开始手势映射测试...")
        print("💡 提示：请确保已启用手势映射模式")
        print("📱 在VR中做不同手势，观察机械手响应和终端分析")
        print("🛑 按 Ctrl+C 查看映射质量报告\n")
        
        rclpy.spin(test_node)
        
    except KeyboardInterrupt:
        print("\n🛑 测试结束")
        test_node.print_mapping_quality_report()
    finally:
        test_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
