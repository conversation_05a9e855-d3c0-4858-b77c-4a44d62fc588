#!/usr/bin/env python3
"""
角度修复验证脚本
用于验证手势映射修复是否生效
"""

import rclpy
from rclpy.node import Node
from my_interfaces.msg import SetJointAngles
import time

class AngleFixVerificationNode(Node):
    def __init__(self):
        super().__init__('angle_fix_verification')
        
        # 订阅Paxini手部命令话题
        self.subscription = self.create_subscription(
            SetJointAngles,
            '/dexh13_command',
            self.verify_angles_callback,
            10
        )
        
        self.get_logger().info("🔧 角度修复验证节点启动")
        self.get_logger().info("📊 监听 /dexh13_command 话题...")
        
        print("\n" + "="*80)
        print("🎯 角度修复验证测试")
        print("="*80)
        print("📋 请按顺序在VR中做以下手势，观察角度变化：")
        print("   1. 完全张开手掌（五指伸直）- 期望：所有MCP和PIP角度 < 30°")
        print("   2. 握拳（五指弯曲）- 期望：所有MCP和PIP角度 > 60°")
        print("   3. 食指伸直其他弯曲 - 期望：食指角度 < 30°，其他 > 60°")
        print("   4. 三指夹取动作 - 期望：拇指、食指、中指适中角度")
        print("="*80)
        
        self.frame_count = 0
        self.last_print_time = time.time()
        
    def verify_angles_callback(self, msg):
        """验证角度修复效果"""
        self.frame_count += 1
        current_time = time.time()
        
        # 每0.5秒打印一次详细分析
        if current_time - self.last_print_time >= 0.5:
            self.last_print_time = current_time
            
            angles = msg.joint_angles
            
            if len(angles) >= 16:
                self.analyze_gesture_fix(angles)
                
    def analyze_gesture_fix(self, angles):
        """分析手势修复效果"""
        print(f"\n📊 第 {self.frame_count} 帧 - 角度修复验证:")
        print("-" * 60)
        
        # 解析关节角度
        index_mcp_abd = angles[0]    # 食指MCP侧摆
        index_mcp_flex = angles[1]   # 食指MCP屈曲
        index_pip = angles[2]        # 食指PIP
        
        middle_mcp_abd = angles[4]   # 中指MCP侧摆
        middle_mcp_flex = angles[5]  # 中指MCP屈曲
        middle_pip = angles[6]       # 中指PIP
        
        ring_mcp_abd = angles[8]     # 无名指MCP侧摆
        ring_mcp_flex = angles[9]    # 无名指MCP屈曲
        ring_pip = angles[10]        # 无名指PIP
        
        thumb_cmc_abd = angles[12]   # 拇指CMC外展
        thumb_cmc_flex = angles[13]  # 拇指CMC屈曲
        thumb_mcp = angles[14]       # 拇指MCP
        thumb_ip = angles[15]        # 拇指IP
        
        # 显示详细角度
        print(f"🔸 食指: MCP屈曲={index_mcp_flex:5.1f}°, PIP={index_pip:5.1f}° {self.get_finger_status(index_mcp_flex, index_pip)}")
        print(f"🔸 中指: MCP屈曲={middle_mcp_flex:5.1f}°, PIP={middle_pip:5.1f}° {self.get_finger_status(middle_mcp_flex, middle_pip)}")
        print(f"🔸 无名指: MCP屈曲={ring_mcp_flex:5.1f}°, PIP={ring_pip:5.1f}° {self.get_finger_status(ring_mcp_flex, ring_pip)}")
        print(f"🔸 拇指: MCP={thumb_mcp:5.1f}°, IP={thumb_ip:5.1f}° {self.get_finger_status(thumb_mcp, thumb_ip)}")
        
        # 整体手势分析
        avg_finger_flexion = (index_mcp_flex + middle_mcp_flex + ring_mcp_flex) / 3
        
        if avg_finger_flexion < 30:
            gesture_status = "🖐️ 张开手掌"
            fix_status = "✅ 修复正常" if self.is_open_hand_expected() else "⚠️ 检查VR手势"
        elif avg_finger_flexion > 60:
            gesture_status = "✊ 握拳"
            fix_status = "✅ 修复正常" if self.is_closed_fist_expected() else "⚠️ 检查VR手势"
        else:
            gesture_status = "🤏 部分弯曲"
            fix_status = "✅ 修复正常"
            
        print(f"📋 整体状态: {gesture_status} (平均屈曲: {avg_finger_flexion:.1f}°)")
        print(f"🔧 修复状态: {fix_status}")
        
        # 检查是否还有反向问题
        if avg_finger_flexion > 80:
            print("⚠️  如果您此时手掌是张开的，角度修复可能还需要调整")
        elif avg_finger_flexion < 10:
            print("⚠️  如果您此时手掌是握拳的，角度修复可能还需要调整")
        else:
            print("✅ 角度范围正常，修复生效")
            
    def get_finger_status(self, mcp_angle, pip_angle):
        """获取手指状态"""
        avg_angle = (mcp_angle + pip_angle) / 2
        if avg_angle < 30:
            return "🟢伸直"
        elif avg_angle > 60:
            return "🔴弯曲"
        else:
            return "🟡半弯"
            
    def is_open_hand_expected(self):
        """检查是否期望张开手掌（简化判断）"""
        return True  # 简化实现
        
    def is_closed_fist_expected(self):
        """检查是否期望握拳（简化判断）"""
        return True  # 简化实现

def main():
    rclpy.init()
    
    verification_node = AngleFixVerificationNode()
    
    try:
        print("\n🚀 开始角度修复验证...")
        print("💡 提示：请确保已重启VR系统以应用修复")
        print("📱 在VR中做不同手势，观察角度是否正常")
        print("🛑 按 Ctrl+C 停止验证\n")
        
        rclpy.spin(verification_node)
        
    except KeyboardInterrupt:
        print("\n🛑 验证结束")
        print("📊 如果角度仍然反向，请检查VR手势识别质量")
    finally:
        verification_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
