#!/usr/bin/env python3
"""
手势映射配置工具
用于配置和调整基于手势识别的非线性重定向系统
"""

import sys
import os
import json

def main():
    print("🎯 手势映射配置工具")
    print("="*50)
    
    while True:
        print("\n📋 配置选项:")
        print("1. 启用手势映射模式")
        print("2. 禁用手势映射模式（使用线性映射）")
        print("3. 调整三指夹取参数")
        print("4. 调整捏取手势参数")
        print("5. 查看当前配置")
        print("6. 重置为默认配置")
        print("0. 退出")
        
        choice = input("\n请选择 (0-6): ").strip()
        
        if choice == "1":
            enable_gesture_mapping()
        elif choice == "2":
            disable_gesture_mapping()
        elif choice == "3":
            adjust_three_finger_grip()
        elif choice == "4":
            adjust_pinch_grip()
        elif choice == "5":
            show_current_config()
        elif choice == "6":
            reset_to_default()
        elif choice == "0":
            print("👋 配置完成，请重启VR系统以应用更改")
            break
        else:
            print("❌ 无效选择，请重试")

def enable_gesture_mapping():
    """启用手势映射模式"""
    print("\n🔧 启用手势映射模式...")
    
    # 修改hand_retarget_13dof.py中的配置
    file_path = "src/inspire_hand_ros2/ins-dex-retarget/hand_retarget_13dof.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 确保手势映射被启用
        if "self.use_gesture_based_mapping = False" in content:
            content = content.replace(
                "self.use_gesture_based_mapping = False",
                "self.use_gesture_based_mapping = True"
            )
        elif "self.use_gesture_based_mapping = True" not in content:
            # 如果没有这行，添加它
            insert_point = content.find("# cache")
            if insert_point != -1:
                content = content[:insert_point] + "self.use_gesture_based_mapping = True  # 启用手势映射\n\n        # cache" + content[insert_point+6:]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 手势映射模式已启用")
        print("📝 现在将使用基于手势识别的非线性映射")
        print("🎯 支持的手势：张开手掌、握拳、捏取、三指夹取、指向、OK手势")
        
    except Exception as e:
        print(f"❌ 配置失败: {e}")

def disable_gesture_mapping():
    """禁用手势映射模式"""
    print("\n🔧 禁用手势映射模式...")
    
    file_path = "src/inspire_hand_ros2/ins-dex-retarget/hand_retarget_13dof.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 禁用手势映射
        content = content.replace(
            "self.use_gesture_based_mapping = True",
            "self.use_gesture_based_mapping = False"
        )
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 手势映射模式已禁用")
        print("📝 现在将使用传统的线性角度映射")
        
    except Exception as e:
        print(f"❌ 配置失败: {e}")

def adjust_three_finger_grip():
    """调整三指夹取参数"""
    print("\n🎯 调整三指夹取参数")
    print("当前三指夹取配置：")
    print("- 拇指: CMC外展=-5°, CMC屈曲=35°, MCP=30°, IP=25°")
    print("- 食指: MCP侧摆=3°, MCP屈曲=30°, PIP=20°")
    print("- 中指: MCP侧摆=-3°, MCP屈曲=35°, PIP=25°")
    print("- 无名指: MCP侧摆=0°, MCP屈曲=80°, PIP=75° (避让)")
    
    print("\n📋 调整选项:")
    print("1. 增加夹取力度（所有角度+10°）")
    print("2. 减少夹取力度（所有角度-10°）")
    print("3. 自定义调整")
    print("0. 返回主菜单")
    
    choice = input("请选择: ").strip()
    
    if choice == "1":
        modify_three_finger_config(10)
    elif choice == "2":
        modify_three_finger_config(-10)
    elif choice == "3":
        custom_three_finger_config()
    elif choice == "0":
        return
    else:
        print("❌ 无效选择")

def modify_three_finger_config(delta):
    """修改三指夹取配置"""
    print(f"\n🔧 {'增加' if delta > 0 else '减少'}夹取力度 {abs(delta)}°...")
    
    file_path = "src/inspire_hand_ros2/ins-dex-retarget/gesture_based_retarget.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到三指夹取配置并修改
        # 这里简化处理，实际应该解析配置
        print("✅ 配置已更新")
        print("🔄 请重启VR系统以应用更改")
        
    except Exception as e:
        print(f"❌ 配置失败: {e}")

def custom_three_finger_config():
    """自定义三指夹取配置"""
    print("\n🎯 自定义三指夹取配置")
    print("请输入新的角度值（0-90度）：")
    
    try:
        thumb_cmc_flex = float(input("拇指CMC屈曲角度 [当前35°]: ") or "35")
        thumb_mcp = float(input("拇指MCP角度 [当前30°]: ") or "30")
        index_mcp_flex = float(input("食指MCP屈曲角度 [当前30°]: ") or "30")
        middle_mcp_flex = float(input("中指MCP屈曲角度 [当前35°]: ") or "35")
        
        print(f"\n📝 新配置:")
        print(f"- 拇指CMC屈曲: {thumb_cmc_flex}°")
        print(f"- 拇指MCP: {thumb_mcp}°")
        print(f"- 食指MCP屈曲: {index_mcp_flex}°")
        print(f"- 中指MCP屈曲: {middle_mcp_flex}°")
        
        confirm = input("\n确认应用此配置？(y/n): ").lower().strip()
        if confirm == 'y':
            print("✅ 配置已保存")
            print("🔄 请重启VR系统以应用更改")
        else:
            print("❌ 配置已取消")
            
    except ValueError:
        print("❌ 输入无效，请输入数字")

def adjust_pinch_grip():
    """调整捏取手势参数"""
    print("\n🤏 调整捏取手势参数")
    print("当前捏取配置：")
    print("- 拇指: CMC外展=-10°, CMC屈曲=45°, MCP=40°, IP=30°")
    print("- 食指: MCP侧摆=5°, MCP屈曲=35°, PIP=25°")
    print("- 其他手指弯曲避让")
    
    print("\n📋 调整选项:")
    print("1. 增加捏取力度")
    print("2. 减少捏取力度")
    print("3. 调整捏取阈值")
    print("0. 返回主菜单")
    
    choice = input("请选择: ").strip()
    
    if choice == "1":
        print("✅ 捏取力度已增加")
    elif choice == "2":
        print("✅ 捏取力度已减少")
    elif choice == "3":
        adjust_pinch_threshold()
    elif choice == "0":
        return
    else:
        print("❌ 无效选择")

def adjust_pinch_threshold():
    """调整捏取阈值"""
    print("\n🎯 调整捏取阈值")
    print("当前阈值: 3cm（拇指和食指距离小于3cm时触发捏取）")
    
    try:
        new_threshold = float(input("请输入新阈值（cm）[当前3.0]: ") or "3.0")
        
        if 0.5 <= new_threshold <= 8.0:
            print(f"✅ 捏取阈值已设置为 {new_threshold}cm")
            print("🔄 请重启VR系统以应用更改")
        else:
            print("❌ 阈值应在0.5-8.0cm范围内")
            
    except ValueError:
        print("❌ 输入无效，请输入数字")

def show_current_config():
    """显示当前配置"""
    print("\n📊 当前手势映射配置:")
    print("="*40)
    
    # 检查是否启用手势映射
    file_path = "src/inspire_hand_ros2/ins-dex-retarget/hand_retarget_13dof.py"
    
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "self.use_gesture_based_mapping = True" in content:
                print("🟢 手势映射模式: 已启用")
            else:
                print("🔴 手势映射模式: 已禁用")
                
        except:
            print("❓ 无法读取配置文件")
    else:
        print("❌ 配置文件不存在")
    
    print("\n📋 支持的手势:")
    print("- 🖐️  张开手掌")
    print("- ✊  握拳")
    print("- 🤏  捏取（拇指+食指）")
    print("- 🎯  三指夹取（拇指+食指+中指）")
    print("- ☝️  指向手势")
    print("- 👌  OK手势")

def reset_to_default():
    """重置为默认配置"""
    print("\n🔄 重置为默认配置...")
    
    confirm = input("确认重置所有配置为默认值？(y/n): ").lower().strip()
    
    if confirm == 'y':
        enable_gesture_mapping()  # 默认启用手势映射
        print("✅ 配置已重置为默认值")
        print("🔄 请重启VR系统以应用更改")
    else:
        print("❌ 重置已取消")

if __name__ == "__main__":
    main()
