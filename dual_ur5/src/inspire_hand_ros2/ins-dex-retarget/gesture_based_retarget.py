"""
基于手势识别的非线性重定向系统
专门针对三指夹取等复杂手势进行优化映射
"""

import numpy as np
from enum import Enum

class GestureType(Enum):
    """手势类型枚举"""
    OPEN_HAND = "open_hand"           # 张开手掌
    CLOSED_FIST = "closed_fist"       # 握拳
    THREE_FINGER_PINCH = "three_finger_pinch"  # 三指夹取
    TWO_FINGER_PINCH = "two_finger_pinch"      # 二指夹取
    INDEX_POINT = "index_point"       # 食指指向
    OK_GESTURE = "ok_gesture"         # OK手势
    UNKNOWN = "unknown"               # 未知手势

class GestureBasedRetarget:
    """基于手势识别的非线性重定向器"""
    
    def __init__(self):
        # 手势识别阈值
        self.pinch_distance_threshold = 0.04  # 4cm
        self.close_distance_threshold = 0.02  # 2cm
        self.finger_bend_threshold = 45.0     # 45度
        
        # 三指夹取的特殊映射参数
        self.three_finger_pinch_params = {
            'thumb_cmc_abd': -10.0,    # 拇指外展角度
            'thumb_cmc_flex': 45.0,    # 拇指CMC屈曲
            'thumb_mcp': 30.0,         # 拇指MCP屈曲
            'thumb_ip': 20.0,          # 拇指IP屈曲
            
            'index_mcp_abd': 0.0,      # 食指MCP侧摆
            'index_mcp_flex': 25.0,    # 食指MCP屈曲
            'index_pip': 15.0,         # 食指PIP屈曲
            
            'middle_mcp_abd': 0.0,     # 中指MCP侧摆
            'middle_mcp_flex': 30.0,   # 中指MCP屈曲
            'middle_pip': 20.0,        # 中指PIP屈曲
            
            'ring_mcp_abd': 0.0,       # 无名指MCP侧摆
            'ring_mcp_flex': 75.0,     # 无名指MCP屈曲（弯曲收起）
            'ring_pip': 80.0,          # 无名指PIP屈曲
        }
        
        # 二指夹取的映射参数
        self.two_finger_pinch_params = {
            'thumb_cmc_abd': -5.0,
            'thumb_cmc_flex': 40.0,
            'thumb_mcp': 25.0,
            'thumb_ip': 15.0,
            
            'index_mcp_abd': 0.0,
            'index_mcp_flex': 20.0,
            'index_pip': 10.0,
            
            'middle_mcp_abd': 0.0,
            'middle_mcp_flex': 80.0,   # 中指弯曲收起
            'middle_pip': 85.0,
            
            'ring_mcp_abd': 0.0,
            'ring_mcp_flex': 80.0,     # 无名指弯曲收起
            'ring_pip': 85.0,
        }
        
        # 张开手掌的映射参数
        self.open_hand_params = {
            'thumb_cmc_abd': 15.0,     # 拇指外展
            'thumb_cmc_flex': 10.0,
            'thumb_mcp': 5.0,
            'thumb_ip': 5.0,
            
            'index_mcp_abd': 0.0,
            'index_mcp_flex': 5.0,
            'index_pip': 5.0,
            
            'middle_mcp_abd': 0.0,
            'middle_mcp_flex': 5.0,
            'middle_pip': 5.0,
            
            'ring_mcp_abd': 0.0,
            'ring_mcp_flex': 5.0,
            'ring_pip': 5.0,
        }
        
        # 握拳的映射参数
        self.closed_fist_params = {
            'thumb_cmc_abd': -15.0,    # 拇指内收
            'thumb_cmc_flex': 70.0,
            'thumb_mcp': 80.0,
            'thumb_ip': 75.0,
            
            'index_mcp_abd': 0.0,
            'index_mcp_flex': 85.0,
            'index_pip': 90.0,
            
            'middle_mcp_abd': 0.0,
            'middle_mcp_flex': 85.0,
            'middle_pip': 90.0,
            
            'ring_mcp_abd': 0.0,
            'ring_mcp_flex': 85.0,
            'ring_pip': 90.0,
        }
        
    def recognize_gesture(self, landmarks, pinch_distance):
        """识别当前手势类型"""
        if landmarks is None or len(landmarks) < 25:
            return GestureType.UNKNOWN
            
        try:
            # 计算关键距离
            thumb_tip = landmarks[4]      # 拇指尖
            index_tip = landmarks[8]      # 食指尖
            middle_tip = landmarks[12]    # 中指尖
            ring_tip = landmarks[16]      # 无名指尖
            
            # 拇指到食指距离
            thumb_index_dist = np.linalg.norm(thumb_tip - index_tip)
            # 拇指到中指距离
            thumb_middle_dist = np.linalg.norm(thumb_tip - middle_tip)
            
            # 计算手指弯曲程度（简化计算）
            index_bend = self._calculate_finger_bend(landmarks, [5, 6, 7, 8])
            middle_bend = self._calculate_finger_bend(landmarks, [9, 10, 11, 12])
            ring_bend = self._calculate_finger_bend(landmarks, [13, 14, 15, 16])
            
            # 手势识别逻辑
            if (thumb_index_dist < self.close_distance_threshold and 
                thumb_middle_dist < self.pinch_distance_threshold and
                ring_bend > self.finger_bend_threshold):
                return GestureType.THREE_FINGER_PINCH
                
            elif (thumb_index_dist < self.close_distance_threshold and
                  middle_bend > self.finger_bend_threshold and
                  ring_bend > self.finger_bend_threshold):
                return GestureType.TWO_FINGER_PINCH
                
            elif (index_bend < 20 and middle_bend > 60 and ring_bend > 60):
                return GestureType.INDEX_POINT
                
            elif (index_bend > 70 and middle_bend > 70 and ring_bend > 70):
                return GestureType.CLOSED_FIST
                
            elif (index_bend < 30 and middle_bend < 30 and ring_bend < 30):
                return GestureType.OPEN_HAND
                
            else:
                return GestureType.UNKNOWN
                
        except Exception as e:
            print(f"手势识别错误: {e}")
            return GestureType.UNKNOWN
    
    def _calculate_finger_bend(self, landmarks, indices):
        """计算手指弯曲程度（简化版本）"""
        try:
            if len(indices) < 4:
                return 0.0
                
            # 使用指根到指尖的直线距离与实际路径长度的比值来估算弯曲程度
            base = landmarks[indices[0]]
            tip = landmarks[indices[-1]]
            
            # 直线距离
            straight_dist = np.linalg.norm(tip - base)
            
            # 实际路径长度
            path_dist = 0.0
            for i in range(len(indices) - 1):
                path_dist += np.linalg.norm(landmarks[indices[i+1]] - landmarks[indices[i]])
            
            if straight_dist > 0:
                bend_ratio = (path_dist - straight_dist) / straight_dist
                return min(bend_ratio * 100, 90.0)  # 转换为角度
            else:
                return 0.0
                
        except:
            return 0.0
    
    def retarget_to_robot(self, gesture_type, landmarks, pinch_distance):
        """根据手势类型进行非线性重定向到机械手角度"""
        
        if gesture_type == GestureType.THREE_FINGER_PINCH:
            params = self._get_adaptive_three_finger_params(landmarks, pinch_distance)
        elif gesture_type == GestureType.TWO_FINGER_PINCH:
            params = self.two_finger_pinch_params
        elif gesture_type == GestureType.OPEN_HAND:
            params = self.open_hand_params
        elif gesture_type == GestureType.CLOSED_FIST:
            params = self.closed_fist_params
        else:
            # 对于未知手势，使用线性映射的结果
            return self._fallback_linear_mapping(landmarks)
        
        # 构建13DOF角度数组
        # 顺序：Thumb(4) + Ring(3) + Middle(3) + Index(3) = 13 DOF
        angles = np.array([
            params['thumb_cmc_abd'],    # 0: 拇指CMC外展
            params['thumb_cmc_flex'],   # 1: 拇指CMC屈曲
            params['thumb_mcp'],        # 2: 拇指MCP
            params['thumb_ip'],         # 3: 拇指IP
            
            params['ring_mcp_abd'],     # 4: 无名指MCP侧摆
            params['ring_mcp_flex'],    # 5: 无名指MCP屈曲
            params['ring_pip'],         # 6: 无名指PIP
            
            params['middle_mcp_abd'],   # 7: 中指MCP侧摆
            params['middle_mcp_flex'],  # 8: 中指MCP屈曲
            params['middle_pip'],       # 9: 中指PIP
            
            params['index_mcp_abd'],    # 10: 食指MCP侧摆
            params['index_mcp_flex'],   # 11: 食指MCP屈曲
            params['index_pip'],        # 12: 食指PIP
        ])
        
        return angles
    
    def _get_adaptive_three_finger_params(self, landmarks, pinch_distance):
        """获取自适应的三指夹取参数"""
        params = self.three_finger_pinch_params.copy()
        
        # 根据pinch_distance动态调整角度
        if pinch_distance < 0.015:  # 非常接近，需要更精确的夹取
            params['thumb_mcp'] = 35.0
            params['thumb_ip'] = 25.0
            params['index_mcp_flex'] = 30.0
            params['index_pip'] = 20.0
            params['middle_mcp_flex'] = 35.0
            params['middle_pip'] = 25.0
        elif pinch_distance > 0.03:  # 距离较远，预备夹取姿态
            params['thumb_mcp'] = 20.0
            params['thumb_ip'] = 10.0
            params['index_mcp_flex'] = 15.0
            params['index_pip'] = 8.0
            params['middle_mcp_flex'] = 20.0
            params['middle_pip'] = 12.0
            
        return params
    
    def _fallback_linear_mapping(self, landmarks):
        """线性映射的后备方案"""
        # 返回中性位置
        return np.array([0.0, 30.0, 30.0, 30.0,  # 拇指
                        0.0, 40.0, 40.0,          # 无名指
                        0.0, 40.0, 40.0,          # 中指
                        0.0, 40.0, 40.0])         # 食指
