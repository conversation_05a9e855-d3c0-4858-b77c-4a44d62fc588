#!/usr/bin/env python3
"""
基于手势识别的非线性重定向系统
解决人手、VR关键点和机械手之间的运动学差异
Author: AI Assistant
Date: 2025-01-11
"""

import numpy as np
from enum import Enum
import math

class GestureType(Enum):
    """手势类型枚举"""
    OPEN_HAND = "open_hand"           # 张开手掌
    CLOSED_FIST = "closed_fist"       # 握拳
    PINCH_GRIP = "pinch_grip"         # 捏取（拇指+食指）
    THREE_FINGER_GRIP = "three_finger_grip"  # 三指夹取
    POINT_GESTURE = "point_gesture"   # 指向手势
    OK_GESTURE = "ok_gesture"         # OK手势
    UNKNOWN = "unknown"               # 未知手势

class GestureBasedRetarget:
    """基于手势识别的非线性重定向器"""
    
    def __init__(self):
        # 手势识别参数
        self.pinch_threshold = 0.03  # 拇指食指距离阈值（3cm）
        self.finger_bend_threshold = 0.5  # 手指弯曲阈值
        self.gesture_stability_frames = 5  # 手势稳定帧数
        
        # 手势历史记录（用于稳定性）
        self.gesture_history = []
        self.last_stable_gesture = GestureType.OPEN_HAND
        
        # 预定义的机械手手势配置
        self.robot_gesture_configs = self._init_robot_gesture_configs()
        
        # 平滑过渡参数
        self.transition_speed = 0.15  # 过渡速度（0-1）
        self.last_robot_angles = None
        
    def _init_robot_gesture_configs(self):
        """初始化机械手手势配置"""
        configs = {}
        
        # 张开手掌配置
        configs[GestureType.OPEN_HAND] = {
            'thumb': [0, 10, 5, 5],      # [CMC_abd, CMC_flex, MCP, IP]
            'index': [0, 5, 5],          # [MCP_abd, MCP_flex, PIP]
            'middle': [0, 5, 5],         # [MCP_abd, MCP_flex, PIP]
            'ring': [0, 10, 10]          # [MCP_abd, MCP_flex, PIP]
        }
        
        # 握拳配置
        configs[GestureType.CLOSED_FIST] = {
            'thumb': [10, 80, 70, 60],   # 拇指包住其他手指
            'index': [0, 85, 80],        # 食指完全弯曲
            'middle': [0, 85, 80],       # 中指完全弯曲
            'ring': [0, 80, 75]          # 无名指完全弯曲
        }
        
        # 捏取配置（拇指+食指）
        configs[GestureType.PINCH_GRIP] = {
            'thumb': [-10, 45, 40, 30],  # 拇指向食指靠拢
            'index': [5, 35, 25],        # 食指适度弯曲
            'middle': [0, 70, 65],       # 中指弯曲避让
            'ring': [0, 75, 70]          # 无名指弯曲避让
        }
        
        # 三指夹取配置（拇指+食指+中指）
        configs[GestureType.THREE_FINGER_GRIP] = {
            'thumb': [-5, 35, 30, 25],   # 拇指适度弯曲
            'index': [3, 30, 20],        # 食指轻度弯曲
            'middle': [-3, 35, 25],      # 中指轻度弯曲
            'ring': [0, 80, 75]          # 无名指完全弯曲避让
        }
        
        # 指向手势配置
        configs[GestureType.POINT_GESTURE] = {
            'thumb': [5, 60, 50, 40],    # 拇指弯曲
            'index': [0, 8, 5],          # 食指伸直
            'middle': [0, 80, 75],       # 中指弯曲
            'ring': [0, 85, 80]          # 无名指弯曲
        }
        
        # OK手势配置
        configs[GestureType.OK_GESTURE] = {
            'thumb': [-15, 50, 45, 35],  # 拇指与食指形成圆圈
            'index': [8, 40, 35],        # 食指弯曲形成圆圈
            'middle': [0, 10, 8],        # 中指伸直
            'ring': [0, 10, 8]           # 无名指伸直
        }
        
        return configs
    
    def recognize_gesture(self, finger_landmarks, pinch_distance):
        """识别当前手势"""
        if finger_landmarks is None or len(finger_landmarks) < 25:
            return GestureType.UNKNOWN
            
        # 计算手指弯曲度
        finger_curvatures = self._calculate_finger_curvatures(finger_landmarks)
        
        # 手势识别逻辑
        gesture = self._classify_gesture(finger_curvatures, pinch_distance)
        
        # 添加到历史记录
        self.gesture_history.append(gesture)
        if len(self.gesture_history) > self.gesture_stability_frames:
            self.gesture_history.pop(0)
            
        # 检查手势稳定性
        if len(self.gesture_history) >= self.gesture_stability_frames:
            if all(g == gesture for g in self.gesture_history[-3:]):  # 最近3帧一致
                self.last_stable_gesture = gesture
                
        return self.last_stable_gesture
    
    def _calculate_finger_curvatures(self, landmarks):
        """计算手指弯曲度"""
        curvatures = {}
        
        # 食指弯曲度 (landmarks 5-8)
        curvatures['index'] = self._calculate_single_finger_curvature(landmarks, [5, 6, 7, 8])
        
        # 中指弯曲度 (landmarks 9-12)
        curvatures['middle'] = self._calculate_single_finger_curvature(landmarks, [9, 10, 11, 12])
        
        # 无名指弯曲度 (landmarks 13-16)
        curvatures['ring'] = self._calculate_single_finger_curvature(landmarks, [13, 14, 15, 16])
        
        # 拇指弯曲度 (landmarks 1-4)
        curvatures['thumb'] = self._calculate_single_finger_curvature(landmarks, [1, 2, 3, 4])
        
        return curvatures
    
    def _calculate_single_finger_curvature(self, landmarks, indices):
        """计算单个手指的弯曲度"""
        if len(indices) < 4:
            return 0.0
            
        try:
            # 计算手指各段的角度
            angles = []
            for i in range(len(indices) - 2):
                p1 = landmarks[indices[i]]
                p2 = landmarks[indices[i + 1]]
                p3 = landmarks[indices[i + 2]]
                
                # 计算角度
                v1 = p1 - p2
                v2 = p3 - p2
                
                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
                cos_angle = np.clip(cos_angle, -1.0, 1.0)
                angle = np.arccos(cos_angle)
                angles.append(angle)
            
            # 弯曲度 = 平均角度偏离直线的程度
            curvature = np.mean([np.pi - angle for angle in angles]) / np.pi
            return np.clip(curvature, 0.0, 1.0)
            
        except:
            return 0.0
    
    def _classify_gesture(self, curvatures, pinch_distance):
        """分类手势"""
        thumb_curve = curvatures.get('thumb', 0)
        index_curve = curvatures.get('index', 0)
        middle_curve = curvatures.get('middle', 0)
        ring_curve = curvatures.get('ring', 0)
        
        avg_curve = (index_curve + middle_curve + ring_curve) / 3
        
        # 手势分类逻辑
        if pinch_distance < self.pinch_threshold:
            if middle_curve < 0.3:  # 中指也参与
                return GestureType.THREE_FINGER_GRIP
            else:
                return GestureType.PINCH_GRIP
                
        elif avg_curve < 0.2:  # 大部分手指伸直
            if index_curve < 0.15 and middle_curve > 0.6 and ring_curve > 0.6:
                return GestureType.POINT_GESTURE
            else:
                return GestureType.OPEN_HAND
                
        elif avg_curve > 0.7:  # 大部分手指弯曲
            return GestureType.CLOSED_FIST
            
        else:
            # 检查OK手势：拇指和食指弯曲，其他伸直
            if (thumb_curve > 0.4 and index_curve > 0.4 and 
                middle_curve < 0.3 and ring_curve < 0.3):
                return GestureType.OK_GESTURE
            else:
                return GestureType.UNKNOWN
    
    def retarget_to_robot(self, gesture_type, finger_landmarks, pinch_distance):
        """将识别的手势重定向到机械手角度"""
        
        # 获取基础配置
        if gesture_type not in self.robot_gesture_configs:
            gesture_type = GestureType.OPEN_HAND
            
        base_config = self.robot_gesture_configs[gesture_type]
        
        # 应用细微调整（基于实际手指位置）
        adjusted_config = self._apply_fine_adjustments(
            base_config, finger_landmarks, pinch_distance, gesture_type
        )
        
        # 转换为13DOF角度数组
        robot_angles = self._config_to_angles(adjusted_config)
        
        # 平滑过渡
        if self.last_robot_angles is not None:
            robot_angles = self._smooth_transition(self.last_robot_angles, robot_angles)
            
        self.last_robot_angles = robot_angles.copy()
        
        return robot_angles
    
    def _apply_fine_adjustments(self, base_config, landmarks, pinch_distance, gesture_type):
        """应用细微调整"""
        adjusted = base_config.copy()
        
        if gesture_type == GestureType.THREE_FINGER_GRIP:
            # 三指夹取的特殊调整
            # 根据拇指和食指的距离调整夹取力度
            grip_strength = max(0.0, min(1.0, (0.05 - pinch_distance) / 0.03))
            
            # 调整拇指角度
            adjusted['thumb'][1] += grip_strength * 15  # CMC flexion
            adjusted['thumb'][2] += grip_strength * 10  # MCP
            
            # 调整食指角度
            adjusted['index'][1] += grip_strength * 15  # MCP flexion
            adjusted['index'][2] += grip_strength * 10  # PIP
            
            # 调整中指角度
            adjusted['middle'][1] += grip_strength * 12  # MCP flexion
            adjusted['middle'][2] += grip_strength * 8   # PIP
            
        elif gesture_type == GestureType.PINCH_GRIP:
            # 捏取的特殊调整
            grip_strength = max(0.0, min(1.0, (0.04 - pinch_distance) / 0.03))
            
            adjusted['thumb'][1] += grip_strength * 20
            adjusted['thumb'][2] += grip_strength * 15
            adjusted['index'][1] += grip_strength * 20
            adjusted['index'][2] += grip_strength * 15
            
        return adjusted
    
    def _config_to_angles(self, config):
        """将配置转换为13DOF角度数组"""
        # 顺序：Thumb(4) + Ring(3) + Middle(3) + Index(3) = 13 DOF
        angles = np.zeros(13)
        
        # 拇指 (0-3)
        angles[0:4] = config['thumb']
        
        # 无名指 (4-6)
        angles[4:7] = config['ring']
        
        # 中指 (7-9)
        angles[7:10] = config['middle']
        
        # 食指 (10-12)
        angles[10:13] = config['index']
        
        # 确保角度在合理范围内
        angles = np.clip(angles, -20, 90)
        
        return angles
    
    def _smooth_transition(self, last_angles, target_angles):
        """平滑过渡"""
        return last_angles + (target_angles - last_angles) * self.transition_speed
