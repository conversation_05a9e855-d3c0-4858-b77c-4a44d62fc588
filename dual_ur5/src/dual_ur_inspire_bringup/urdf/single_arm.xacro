<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro" name="dual_ur_robotiq">

  <xacro:property name="PI" value="3.1415926535897931"/>

  <link name="world"/>

  <joint name="fixed" type="fixed">
    <parent link="world"/>
    <child link="left_base_link"/>
    <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 ${-pi/2.0}" />
  </joint>


<!-- (1) UR5 -->
  <xacro:include filename="$(find dual_arm_model)/urdf/ur.xacro" />
  <xacro:include filename="$(find dual_arm_model)/urdf/ur.ros2_control.xacro" />

  <!-- left arm -->
  <xacro:ur_robot 
                      tf_prefix="left_"
                      joint_limits_parameters_file="$(find dual_ur_inspire_bringup)/config/ur5/joint_limits.yaml"
                      kinematics_parameters_file="$(find dual_ur_inspire_bringup)/config/ur5/default_kinematics.yaml"
                      physical_parameters_file="$(find dual_ur_inspire_bringup)/config/ur5/physical_parameters.yaml"
                      visual_parameters_file="$(find dual_ur_inspire_bringup)/config/ur5/visual_parameters.yaml"
                      safety_limits="false"
                      safety_pos_margin="0.15"
                      safety_k_position="20"
                      />
  <xacro:ur_arm_ros2_control
                      prefix="left_"
                      robot_ip="************"
                      use_fake_hardware="false"
                      fake_sensor_commands="false"
                      script_filename="$(find ur_client_library)/resources/external_control.urscript"
                      output_recipe_filename="$(find dual_ur_inspire_bringup)/resources/rtde_output_recipe.txt"
                      input_recipe_filename="$(find dual_ur_inspire_bringup)/resources/rtde_input_recipe.txt"
                      hash_kinematics="" 
                      reverse_port="50001"
                      script_sender_port="50002"
                      script_command_port="50004"
                      trajectory_port="50003"
                      />

    <joint name="left_ee_fixed_joint" type="fixed">
    <parent link="left_wrist_3_link" />
    <child link = "left_ee_link" />
    <origin xyz="0.0 0.0 0.0" rpy="${0.0} ${-pi/2.0} ${pi/2.0}" />
  </joint>

  <link name="left_ee_link">
    <collision>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.01 0 0"/>
    </collision>
  </link>

</robot>