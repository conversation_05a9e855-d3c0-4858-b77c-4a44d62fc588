#!/usr/bin/env python3
"""
ULTIMATE TeleVision to Inspire Hand Bridge
Combines:
- Working VR hand tracking from television_inspire_bridge_final.py
- Proper ins-dex-retarget control from television_inspire_bridge_https.py
- REAL ZED CAMERA STREAMING for Quest 3 vision
"""

import math
import numpy as np
import sys
import os
import time
from pathlib import Path
from multiprocessing import Array, Process, shared_memory, Queue, Manager, Event, Semaphore
import threading
import cv2

# Add TeleVision to path
sys.path.append('src/inspire_hand_ros2/TeleVision/teleop')
sys.path.append('src/inspire_hand_ros2/ins-dex-retarget')

from TeleVision import OpenTeleVision
from Preprocessor import VuerPreprocessor
from constants_vuer import tip_indices
from constants_vuer import grd_yup2grd_zup


# ZED Camera import (optional)
try:
    import pyzed.sl as sl
    ZED_AVAILABLE = True
except ImportError:
    ZED_AVAILABLE = False
    sl = None

# ins-dex-retarget imports
from hand_retarget import HandRetarget
from hand_retarget_13dof import HandRetarget13DOF

# ROS2 imports
import rclpy
from rclpy.node import Node
from inspire_hand_interfaces.srv import SetAngle
from rclpy.qos import qos_profile_sensor_data
from geometry_msgs.msg import PoseStamped
from std_msgs.msg import Bool

from pytransform3d import rotations

from my_interfaces.msg import SetJointAngles  # 改为导入消息类型
from geometry_msgs.msg import Vector3

import socket
import json

class GimbalController:
    """UDP client for controlling gimbal servos via bridge"""

    def __init__(self):
        self.enabled = False
        self.sock = None

        # 滤波参数 - 优化以减少舵机负载
        self.last_yaw = 0.0
        self.last_pitch = 0.0
        self.angle_threshold = 0.1  # 增加角度变化阈值，减少无效命令
        self.smoothing_factor = 0.8  # 增加平滑因子，减少抖动

        # 频率控制参数
        self.last_send_time = 0.0
        # self.min_send_interval = 1.0 / 60.0  # 限制为40Hz，减少舵机负载
        self.min_send_interval = 0.0  # 完全移除频率限制
        self.send_count = 0

        try:
            # 创建UDP socket
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.sock.settimeout(0.1)  # 非阻塞发送

            # 测试连接到bridge
            test_data = json.dumps({'yaw': 0, 'pitch': 0}).encode()
            self.sock.sendto(test_data, ('localhost', 9999))

            self.enabled = True
            # print("✓ Gimbal bridge connection established")
            # print(f"✓ Angle filtering enabled (threshold: {self.angle_threshold}°)")

        except Exception as e:
            # print(f"✗ Failed to connect to gimbal bridge: {e}")
            # print("  请确保gimbal_bridge.py正在运行")
            self.enabled = False

    @staticmethod
    def process_head_to_servo_angles(head_mat):
        """
        Process head matrix to servo angles (same as teleop_active_cam.py)

        Args:
            head_mat: 4x4 head transformation matrix

        Returns:
            ypr: [yaw, pitch, roll] angles in radians
            servo_angles: [yaw, pitch] angles for servo control
        """
        # print(f"🔧 Processing head matrix, input shape: {head_mat.shape}")
        # print(f"🔧 Input head_mat:\n{head_mat}")

        # 检查输入矩阵的旋转部分是否为单位矩阵
        rotation_part = head_mat[:3, :3]
        is_identity = np.allclose(rotation_part, np.eye(3), atol=1e-3)  # 放宽容差

        if is_identity:
            # 检查是否有平移分量，如果有则可能是有效的跟踪数据
            translation = head_mat[:3, 3]
            has_translation = np.linalg.norm(translation) > 0.1

            if has_translation:
                # print(f"⚠️  No rotation but has translation {translation}, treating as valid tracking")
                # 即使没有旋转，也返回零角度而不是错误
                return [0.0, 0.0, 0.0], [0.0, 0.0]
            else:
                # print("⚠️  Input is identity matrix - VR tracking not working, returning zero angles")
                return [0.0, 0.0, 0.0], [0.0, 0.0]

        # Extract 3x3 rotation matrix and apply coordinate transformation
        head_rot_mat = grd_yup2grd_zup[:3, :3] @ head_mat[:3, :3] @ grd_yup2grd_zup[:3, :3].T
        # print(f"🔧 Transformed rotation matrix:\n{head_rot_mat}")

        # Safety check for zero matrix
        if np.sum(np.abs(head_rot_mat)) < 1e-6:
            # print("⚠️  Zero rotation matrix detected, returning zero angles")
            return [0.0, 0.0, 0.0], [0.0, 0.0]

        # 检查变换后的矩阵是否仍然是有效的旋转矩阵
        det = np.linalg.det(head_rot_mat)
        if abs(det - 1.0) > 0.1:
            # print(f"⚠️  Invalid rotation matrix (det={det:.3f}), returning zero angles")
            return [0.0, 0.0, 0.0], [0.0, 0.0]

        # Convert to quaternion and then to Euler angles
        head_rot = rotations.quaternion_from_matrix(head_rot_mat)
        try:
            # 使用scipy直接从旋转矩阵提取角度
            from scipy.spatial.transform import Rotation
            r = Rotation.from_matrix(head_rot_mat)

            # 根据测试结果，正确的映射应该是：
            # - 抬头/低头 → 测试显示为ZYX的yaw分量
            # - 左转/右转 → 测试显示为ZYX的roll分量
            # - 头部倾斜 → 测试显示为ZYX的pitch分量（应忽略）

            euler_zyx = r.as_euler('zyx', degrees=False)  # 返回弧度
            # print(f"🔧 Raw euler angles (ZYX): {np.degrees(euler_zyx)} degrees")

            # 重新映射：
            yaw_angle = euler_zyx[2]  # roll分量 → yaw（左右转）
            pitch_angle = -euler_zyx[0]  # yaw分量 → pitch（抬头低头），反转符号
            roll_angle = euler_zyx[1]  # pitch分量 → roll（倾斜，忽略）
            # yaw_angle = euler_zyx[0]  # Z轴旋转 → 偏航
            # pitch_angle = euler_zyx[1]  # Y轴旋转 → 俯仰
            # roll_angle = euler_zyx[2]  # pitch分量 → roll（倾斜，忽略）
            # 限制角度范围，防止极限值
            yaw_angle = np.clip(yaw_angle, -np.pi / 2, np.pi / 2)  # ±90度
            pitch_angle = np.clip(pitch_angle, -np.pi / 2, np.pi / 2)  # ±90度

            # print(f"🔧 Remapped angles - yaw: {np.degrees(yaw_angle):.2f}°, pitch: {np.degrees(pitch_angle):.2f}°, roll: {np.degrees(roll_angle):.2f}°")

            # 组织为YPR格式
            ypr = [yaw_angle, pitch_angle, roll_angle]
            servo_angles = [yaw_angle, pitch_angle]  # Only yaw and pitch for servo control
            # print(f"🔧 Final servo angles (radians): {servo_angles}")
            return ypr, servo_angles
        except Exception as e:
            # print(f"❌ Error in angle conversion: {e}")
            return [0.0, 0.0, 0.0], [0.0, 0.0]

    def cleanup(self):
        """Clean up UDP resources"""
        if self.sock:
            try:
                self.sock.close()
            except:
                pass

class VuerTeleopUltimate:
    def __init__(self, config_file_path):
        # EXACT same configuration as working test
        # self.resolution = (1080, 1920)
        self.resolution = (720, 1280)
        self.crop_size_w = 1  # Updated to match teleop_active_cam.py
        self.crop_size_h = 0
        self.resolution_cropped = (
            self.resolution[0] - self.crop_size_h,
            self.resolution[1] - 2 * self.crop_size_w
        )
        
        self.img_shape = (self.resolution_cropped[0], 2 * self.resolution_cropped[1], 3)
        self.img_height, self.img_width = self.resolution_cropped[:2]
        
        # Initialize ZED Camera
        if ZED_AVAILABLE:
            self.zed = sl.Camera()
            
            # Create camera initialization parameters
            init_params = sl.InitParameters()
            # init_params.camera_resolution = sl.RESOLUTION.HD1080 
            init_params.camera_resolution = sl.RESOLUTION.HD720 # Use HD720 video mode
            # Use HD720 video mode
            init_params.camera_fps = 60  # Use 30 FPS (more compatible than 60)
            
            # Open the camera
            err = self.zed.open(init_params)
            if err != sl.ERROR_CODE.SUCCESS:
                print(f"❌ Camera Open Error: {repr(err)}")
                self.zed = None
            else:

                # self.zed.set_camera_settings(sl.VIDEO_SETTINGS.EXPOSURE, 90)
                # self.zed.set_camera_settings(sl.VIDEO_SETTINGS.GAIN, 61)

                # Create image containers
                self.image_left = sl.Mat()
                self.image_right = sl.Mat()
                self.runtime_parameters = sl.RuntimeParameters()
        else:
            self.zed = None
        
        # Create shared memory for images (CRITICAL for TeleVision)
        self.shm = shared_memory.SharedMemory(create=True, size=np.prod(self.img_shape) * np.uint8().itemsize)
        self.img_array = np.ndarray(self.img_shape, dtype=np.uint8, buffer=self.shm.buf)
        
        # Fill with initial images (dummy or camera)
        if self.zed is not None:
            self._capture_initial_frame()
        else:
            # Fall back to dummy images
            left_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
            right_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
            self.img_array[:] = np.hstack((left_img, right_img))
        
        image_queue = Queue()
        toggle_streaming = Event()
        
        # EXACT same OpenTeleVision initialization (DEFAULT parameters - this is KEY!)
        # self.tv = OpenTeleVision(self.resolution_cropped, self.shm.name, image_queue, toggle_streaming)
        self.tv = OpenTeleVision(self.resolution_cropped, self.shm.name, image_queue, toggle_streaming, ngrok=True)
        
        # Initialize preprocessor
        self.processor = VuerPreprocessor()
        
        # Initialize hand retargeting (THE KEY ADDITION!)
        # self.hand_retarget = HandRetarget()
        self.hand_retarget13dof = HandRetarget13DOF()
        
        # Start continuous image update thread (CRITICAL)
        self.image_thread_running = True
        self.image_thread = threading.Thread(target=self.update_images_continuously, daemon=True)
        self.image_thread.start()
    
    def _capture_initial_frame(self):
        """Capture initial frame from ZED camera."""
        if self.zed is not None:
            if self.zed.grab(self.runtime_parameters) == sl.ERROR_CODE.SUCCESS:
                self.zed.retrieve_image(self.image_left, sl.VIEW.LEFT)
                self.zed.retrieve_image(self.image_right, sl.VIEW.RIGHT)
                
                # Process images like in teleop_active_cam.py
                bgr = np.hstack((
                    self.image_left.numpy()[self.crop_size_h:, self.crop_size_w:-self.crop_size_w],
                    self.image_right.numpy()[self.crop_size_h:, self.crop_size_w:-self.crop_size_w]
                ))
                rgb = cv2.cvtColor(bgr, cv2.COLOR_BGRA2RGB)
                np.copyto(self.img_array, rgb)
    
    def update_images_continuously(self):
        """Continuously update the image array with ZED camera feed or dummy images."""
        frame_count = 0
        
        while self.image_thread_running:
            try:
                if self.zed is not None:
                    # Capture real ZED camera images
                    if self.zed.grab(self.runtime_parameters) == sl.ERROR_CODE.SUCCESS:
                        self.zed.retrieve_image(self.image_left, sl.VIEW.LEFT)
                        self.zed.retrieve_image(self.image_right, sl.VIEW.RIGHT)
                        
                        # Process images (crop and convert color space)
                        bgr = np.hstack((
                            self.image_left.numpy()[self.crop_size_h:, self.crop_size_w:-self.crop_size_w],
                            self.image_right.numpy()[self.crop_size_h:, self.crop_size_w:-self.crop_size_w]
                        ))
                        rgb = cv2.cvtColor(bgr, cv2.COLOR_BGRA2RGB)
                        
                        # Update the shared image array
                        np.copyto(self.img_array, rgb)
                        
                        frame_count += 1
                    else:
                        # Fall back to dummy images
                        left_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                        right_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                        np.copyto(self.img_array, np.hstack((left_img, right_img)))
                else:
                    # Use dummy images if no camera
                    left_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                    right_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                    np.copyto(self.img_array, np.hstack((left_img, right_img)))
                
                time.sleep(1.0/60.0)  # 60 FPS
                
            except Exception as e:
                print(f"❌ Error updating images: {e}")
                # Fall back to dummy images on error
                try:
                    left_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                    right_img = np.random.randint(0, 255, (self.img_height, self.img_width, 3), dtype=np.uint8)
                    np.copyto(self.img_array, np.hstack((left_img, right_img)))
                except:
                    pass
                time.sleep(0.1)
    
    def calculate_pinch_distance(self, finger_landmarks):
        """Calculate distance between thumb tip and index finger tip."""
        if finger_landmarks is None or len(finger_landmarks) < 25:
            return 0.1  # Default distance
            
        # Thumb tip is index 4, index finger tip is index 8 in the landmarks
        thumb_tip = finger_landmarks[4]
        index_tip = finger_landmarks[8]
        
        distance = np.linalg.norm(thumb_tip - index_tip)
        return distance
    
    def create_finger_frames_from_landmarks(self, landmarks, wrist_mat):
        """Convert landmarks to finger frames format expected by retargeting."""
        if landmarks is None or len(landmarks) < 25:
            # Return zero frames if no valid data
            return np.zeros((25, 4, 4))
        
        frames = np.zeros((25, 4, 4))
        
        # Set identity matrices as base
        for i in range(25):
            frames[i] = np.eye(4)
            if i < len(landmarks):
                frames[i][:3, 3] = landmarks[i]
        
        return frames
    
    def step(self):
        """Main step function - processes hand data and returns retargeted angles."""
        try:
            # Get preprocessed hand data (following teleop_hand.py pattern)
            # head_mat, left_wrist_mat, right_wrist_mat, left_hand_landmarks, right_hand_landmarks = self.processor.process(self.tv)
            process_start = time.time()
            head_mat, left_wrist_mat, right_wrist_mat, left_hand_landmarks, right_hand_landmarks = self.processor.process_for_controller(self.tv)
            process_time = time.time() - process_start

            # Check if we have valid hand data
            landmarks_valid = False
            if (hasattr(self.tv, 'left_landmarks') and hasattr(self.tv, 'right_landmarks')):
                left_nonzero = np.any(self.tv.left_landmarks != 0)
                right_nonzero = np.any(self.tv.right_landmarks != 0)
                landmarks_valid = left_nonzero or right_nonzero
            
            # landmarks_valid = True
            if landmarks_valid:
                # Calculate pinch distances
                left_pinch_distance = self.calculate_pinch_distance(left_hand_landmarks)
                right_pinch_distance = self.calculate_pinch_distance(right_hand_landmarks)
                
                # Create finger frames for retargeting
                left_finger_frames = self.create_finger_frames_from_landmarks(left_hand_landmarks, left_wrist_mat)
                right_finger_frames = self.create_finger_frames_from_landmarks(right_hand_landmarks, right_wrist_mat)
                
                # Create data structure expected by hand_retarget
                retarget_data = {
                    "left_fingers": left_finger_frames,
                    "right_fingers": right_finger_frames,
                    "left_pinch_distance": left_pinch_distance,
                    "right_pinch_distance": right_pinch_distance
                }
                
                # Get retargeted angles using ins-dex-retarget
                # left_angles, right_angles = self.hand_retarget.solve_fingers_angles(retarget_data)
                left_angles, right_angles = self.hand_retarget13dof.solve_fingers_angles(retarget_data)

                return {
                    'valid': True,
                    'left_angles': left_angles,
                    'right_angles': right_angles,
                    'left_landmarks': self.tv.left_landmarks,
                    'right_landmarks': self.tv.right_landmarks,
                    'left_hand_landmarks': left_hand_landmarks,
                    'right_hand_landmarks': right_hand_landmarks,
                    'left_wrist_mat':left_wrist_mat,
                    'right_wrist_mat':right_wrist_mat,
                    'head_mat': head_mat
                }
            else:
                return {
                    'valid': False,
                    'left_angles': np.full(13, 30.0),  # 13个关节角度
                    'right_angles': np.full(13, 30.0), # 13个关节角度
                    'left_landmarks': self.tv.left_landmarks if hasattr(self.tv, 'left_landmarks') else np.zeros((25, 3)),
                    'right_landmarks': self.tv.right_landmarks if hasattr(self.tv, 'right_landmarks') else np.zeros((25, 3)),
                    'left_wrist_mat': np.eye(4),
                    'right_wrist_mat': np.eye(4),
                    'head_mat': head_mat
                }
                
        except Exception as e:
            print(f"Error in step: {e}")
            return {
                'head_mat': np.eye(4),
                'valid': False,
                'left_angles': np.full(13, 30.0),  # 13个关节角度
                'right_angles': np.full(13, 30.0), # 13个关节角度
                'left_landmarks': np.zeros((25, 3)),
                'right_landmarks': np.zeros((25, 3)),
                'left_wrist_mat': np.eye(4),
                'right_wrist_mat': np.eye(4)

            }
    
    def cleanup(self):
        """Clean up resources."""
        try:
            # Stop image capture thread
            self.image_thread_running = False
            if hasattr(self, 'image_thread'):
                self.image_thread.join(timeout=1.0)
            
            # Close ZED camera
            if hasattr(self, 'zed') and self.zed is not None:
                self.zed.close()
            
            # Clean up shared memory
            if hasattr(self, 'shm'):
                self.shm.close()
                self.shm.unlink()
                
        except Exception as e:
            print(f"⚠️  Error during cleanup: {e}")


class InspireHandController(Node):
    def __init__(self):
        super().__init__('inspire_hand_controller')

        self.declare_parameter('debugging', False)
        self.declare_parameter('not_use_mcp_abduction', True)

        self.debugging = self.get_parameter('debugging').get_parameter_value().bool_value
        self.not_use_mcp_abduction = self.get_parameter('not_use_mcp_abduction').get_parameter_value().bool_value

        # ROS2 service clients for controlling inspire hands
        self.left_hand_client = self.create_client(SetAngle, '/inspire_hand_left/inspire_hand_set_angle_srv')
        self.right_hand_client = self.create_client(SetAngle, '/inspire_hand_right/inspire_hand_set_angle_srv')
        
        self.paxini_publisher = self.create_publisher(SetJointAngles, '/dexh13_command', 10)
        
        self.start_flag_publisher = self.create_publisher(Bool,"/start_flag",qos_profile_sensor_data)
        self.start_flag = Bool()
        self.start_flag.data = False

        self.left_wrist_pose_publisher = self.create_publisher(PoseStamped,"/left/wrist_pose",qos_profile_sensor_data)
        self.right_wrist_pose_publisher = self.create_publisher(PoseStamped,"/right/wrist_pose",qos_profile_sensor_data)
        
        # Control parameters
        self.left_device_id = 2
        self.right_device_id = 1

        # 创建云台角度发布器
        self.gimbal_publisher = self.create_publisher(Vector3, 'gimbal_angles', 10)

        # 初始化云台控制相关属性
        self.last_send_time = 0.0
        self.ultra_responsive_mode = True
        self.min_send_interval = 1.0 / 60.0  # 60Hz
        self.send_count = 0
        self.last_sent_yaw = None
        self.last_sent_pitch = None
        self.gimbal_call_count = 0
        self.same_angle_count = 0

    def send_hand_commands(self, left_angles, right_angles):
        """Send angle commands to Paxini hand (13 DOF)."""
        try:
            # Create Paxini message
            # 13 DOF angles mapping to Paxini hand
            # right_angles structure: [thumb_cmc_abduction, thumb_cmc_flexion, thumb_mcp_flexion, thumb_ip_flexion,
            #                          ring_mcp1_abduction, ring_mcp2_flexion, ring_pip_flexion,
            #                          middle_mcp1_abduction, middle_mcp2_flexion, middle_pip_flexion,
            #                          index_mcp1_abduction, index_mcp2_flexion, index_pip_flexion]
            
            # Direct mapping without normalization since controller accepts 0-90° and -20-20°
            paxini_msg = SetJointAngles()
            
            # 🔧 FIX: Remove incorrect angle averaging that was causing gesture mismatch
            # The original code was incorrectly averaging left hand finger angles
            # and assigning them to right hand thumb, causing inverted gestures

            # Comment out the problematic averaging:
            # right_angles[2] = (left_angles[11] +  left_angles[8] + left_angles[5]) / 3.0
            # right_angles[3] = (left_angles[12] +  left_angles[9] + left_angles[6]) / 3.0

            # Keep the original right_angles values from retargeting instead
                

            if self.not_use_mcp_abduction:
                # right_angles[0] = -20.0
                right_angles[7] = 0
                right_angles[4] = 0
                right_angles[10] = 0

            # 🔧 ENHANCED DEBUGGING: Always show angles for gesture troubleshooting
            if True:  # Force debugging on to help diagnose gesture issues
                print(f"📊 13DOF Angles - Right Hand:")
                print(f"   Thumb:  [{right_angles[0]:6.1f}, {right_angles[1]:6.1f}, {right_angles[2]:6.1f}, {right_angles[3]:6.1f}]")
                print(f"   Ring:   [{right_angles[4]:6.1f}, {right_angles[5]:6.1f}, {right_angles[6]:6.1f}]")
                print(f"   Middle: [{right_angles[7]:6.1f}, {right_angles[8]:6.1f}, {right_angles[9]:6.1f}]")
                print(f"   Index:  [{right_angles[10]:6.1f}, {right_angles[11]:6.1f}, {right_angles[12]:6.1f}]")
                print(f"   📝 Expected: Open hand = low angles (0-20°), Closed fist = high angles (70-90°)")
                print("   " + "="*60)
            
            paxini_msg.joint_angles = [
                # Index finger (3 DOF)
                right_angles[10],  # MCP abduction (-20 to 20)
                right_angles[11],  # MCP flexion (0 to 90) 
                right_angles[12],  # PIP flexion (0 to 90)
                0.0,               # DIP (not controlled)
                
                # Middle finger (3 DOF) 
                right_angles[7],   # MCP abduction (-20 to 20)
                right_angles[8],   # MCP flexion (0 to 90)
                right_angles[9],   # PIP flexion (0 to 90)
                0.0,               # DIP (not controlled)
                
                # Ring finger (3 DOF)
                right_angles[4],   # MCP abduction (-20 to 20)
                right_angles[5],   # MCP flexion (0 to 90)
                right_angles[6],   # PIP flexion (0 to 90)
                0.0,               # DIP (not controlled)
                
                # Thumb (4 DOF)
                right_angles[0],   # CMC abduction (-20 to 20)
                right_angles[1],   # CMC flexion (0 to 90)
                right_angles[2],   # MCP flexion (0 to 90)
                right_angles[3]    # IP flexion (0 to 90)
            ]
            
            self.paxini_publisher.publish(paxini_msg)
            
            return True
            
        except Exception as e:
            self.get_logger().error(f'Error sending hand commands: {str(e)}')
            print(f"❌ ERROR in send_hand_commands: {e}")
            return False
        
    def send_wrist_pose(self, left_wrist_mat, right_wrist_mat):
        
        left_msg = PoseStamped()
        right_msg = PoseStamped()
        
        left_msg = self.wrist_pose_to_msg(left_wrist_mat)
        right_msg = self.wrist_pose_to_msg(right_wrist_mat)
        
        self.left_wrist_pose_publisher.publish(left_msg)
        self.right_wrist_pose_publisher.publish(right_msg)
    
    def wrist_pose_to_msg(self, pose_mat):
        
        msg = PoseStamped()
        
        pose = np.concatenate([pose_mat[:3, 3] + np.array([-0.6, 0, 1.6]),
                            rotations.quaternion_from_matrix(pose_mat[:3, :3])[[1, 2, 3, 0]]])
        
        pose[0] = self.clamp(pose[0],-0.6,-0.05)
        pose[2] = self.clamp(pose[2],0.4,1.5)
        
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.pose.position.x = pose[0]
        msg.pose.position.y = pose[1]
        msg.pose.position.z = pose[2]
        
        msg.pose.orientation.x = pose[3]
        msg.pose.orientation.y = pose[4]
        msg.pose.orientation.z = pose[5]
        msg.pose.orientation.w = pose[6]
        
        return msg
    
    def clamp(self, x, lower_limit, upper_limit):
        if lower_limit > upper_limit:
            lower_limit,upper_limit = upper_limit,lower_limit
        return max(lower_limit, min(x,upper_limit))
    
    def is_pub_start_flag(self, left_angles, right_angles):
        """
        检查启动标志 - 基于13DOF手部角度
        检查三根手指的主屈曲角度: ring[5], middle[8], index[11]
        """
        if self.start_flag.data == False:
            left_fingers_closed = (left_angles[5] < 20 and left_angles[8] < 20 and left_angles[11] < 20)
            right_fingers_closed = (right_angles[5] < 20 and right_angles[8] < 20 and right_angles[11] < 20)
            if left_fingers_closed and right_fingers_closed:
                self.start_flag.data = True
        else:
            left_stop = (left_angles[11] < 20 and left_angles[5] > 80 and left_angles[8] > 80)  # 食指伸出，中指无名指收起
            right_stop = (right_angles[11] < 20 and right_angles[5] > 80 and right_angles[8] > 80)
            if left_stop and right_stop:
                self.start_flag.data = False

        self.start_flag_publisher.publish(self.start_flag) 


    def send_to_gimbal(self, yaw_deg, pitch_deg):
        """发送角度到云台服务 - 优化版本带频率控制"""
        current_time = time.time()

        # 超响应模式：移除频率限制
        if not self.ultra_responsive_mode:
            # 频率控制：检查是否满足最小发送间隔
            if current_time - self.last_send_time < self.min_send_interval:
                # print(f"⏸️  发送频率过高，跳过 (间隔: {current_time - self.last_send_time:.3f}s)")
                return True  # 返回True避免上层认为是错误

        # print(f"🚀 send_to_gimbal called with yaw={yaw_deg:.2f}°, pitch={pitch_deg:.2f}°")

        # 初始化上次发送的角度记录
        if not hasattr(self, 'last_sent_yaw'):
            self.last_sent_yaw = None
            self.last_sent_pitch = None
            self.gimbal_call_count = 0
            self.same_angle_count = 0

        self.gimbal_call_count += 1
        self.send_count += 1

        # 限制角度范围
        original_yaw = yaw_deg
        original_pitch = pitch_deg
        yaw_deg = max(-120, min(120, yaw_deg))
        pitch_deg = max(-120, min(120, pitch_deg))

        if original_yaw != yaw_deg or original_pitch != pitch_deg:
            # print(f"📏 Angles clamped: yaw {original_yaw:.2f}° → {yaw_deg:.2f}°, pitch {original_pitch:.2f}° → {pitch_deg:.2f}°")
            pass

        # 超响应模式：极小阈值或不过滤
        if self.ultra_responsive_mode:
            # 超响应模式：只过滤完全相同的值
            angle_threshold = 0.01
            max_same_commands = 1
        else:
            # 标准模式
            angle_threshold = 0.1
            max_same_commands = 5

        # 检查是否与上次发送的角度相同
        if (self.last_sent_yaw is not None and self.last_sent_pitch is not None and
                abs(yaw_deg - self.last_sent_yaw) < angle_threshold and
                abs(pitch_deg - self.last_sent_pitch) < angle_threshold):

            self.same_angle_count += 1

            # 根据模式决定是否跳过
            if self.same_angle_count > max_same_commands:
                if self.same_angle_count % 50 == 0:  # 每50次打印一次
                    # print(f"⏸️  Skipping duplicate angle command (#{self.same_angle_count}): yaw={yaw_deg:.2f}°, pitch={pitch_deg:.2f}°")
                    pass
                return True
        else:
            # 角度有变化，重置计数器
            self.same_angle_count = 0

        try:
            gimbal_msg = Vector3()
            gimbal_msg.x = float(yaw_deg)
            gimbal_msg.y = float(pitch_deg)
            gimbal_msg.z = 0.0
            
            self.gimbal_publisher.publish(gimbal_msg)

            self.last_sent_yaw = yaw_deg
            self.last_sent_pitch = pitch_deg
            self.last_send_time = current_time

            # 简单的成功日志
            # self.get_logger().info(f'📡 发送云台角度 #{self.send_count}: yaw={yaw_deg:.1f}°, pitch={pitch_deg:.1f}°')
            return True

        except Exception as e:
            print(f"❌ Exception in send_to_gimbal: {e}")
            self.get_logger().error(f'发送角度错误: {e}')
            return False

    def send_angles(self, yaw_deg, pitch_deg):
        """Send yaw and pitch angles to gimbal bridge with filtering"""
        if not self.enabled or not self.sock:
            return False

        try:
            # Limit angles to servo range
            yaw_deg = max(-120, min(120, yaw_deg))
            pitch_deg = max(-120, min(120, pitch_deg))

            # 应用平滑滤波
            smoothed_yaw = self.smoothing_factor * self.last_yaw + (1 - self.smoothing_factor) * yaw_deg
            smoothed_pitch = self.smoothing_factor * self.last_pitch + (1 - self.smoothing_factor) * pitch_deg

            # 检查角度变化是否超过阈值
            yaw_change = abs(smoothed_yaw - self.last_yaw)
            pitch_change = abs(smoothed_pitch - self.last_pitch)

            # 只有变化超过阈值时才发送
            if yaw_change > self.angle_threshold or pitch_change > self.angle_threshold:

                self.send_to_gimbal(smoothed_yaw, smoothed_pitch)

                # 创建数据包
                # data = json.dumps({
                #     'yaw': smoothed_yaw,
                #     'pitch': smoothed_pitch
                # }).encode()

                # 发送到bridge
                # self.sock.sendto(data, ('localhost', 9999))

                # 更新上次发送的角度
                self.last_yaw = smoothed_yaw
                self.last_pitch = smoothed_pitch

                return True
            else:
                # 角度变化太小，不发送但更新内部状态
                self.last_yaw = smoothed_yaw
                self.last_pitch = smoothed_pitch
                return True  # 返回True表示处理成功，即使没有发送

        except Exception as e:
            # print(f"Error sending gimbal angles: {e}")
            return False



def ros_spin_thread(node):
    while rclpy.ok():
        rclpy.spin_once(node, timeout_sec=0.01)

def main():
    # Initialize ROS2
    rclpy.init()
    
    # Create inspire hand controller
    hand_controller = InspireHandController()
    
    # Create VuerTeleop with ZED camera streaming
    config_path = Path("./TeleVision/teleop/inspire_hand.yml")
    teleoperator = VuerTeleopUltimate(config_path)
    
    frame_count = 0
    last_angles_left = None
    last_angles_right = None

    spin_thread = threading.Thread(target=ros_spin_thread, args=(hand_controller,))
    spin_thread.daemon = True
    spin_thread.start()
    
    try:
        while True:
            # Get hand data and retargeted angles + wrist poses
            result = teleoperator.step()
            
            frame_count += 1

            # 每10帧打印一次基础信息
            # if frame_count % 10 == 0:
            # print(f"🔄 Frame {frame_count}: Processing...")
            # print(f"🔍 Result keys: {list(result.keys())}")
            # print(f"🔍 Result valid: {result.get('valid', 'KEY_NOT_FOUND')}")
            # pass

            # Send commands (every frame for maximum responsiveness)
            # 🔧 修改：不管valid状态如何，都尝试处理头显数据
            # print(f"🔍 Frame {frame_count}: result valid = {result['valid']}")

            # 处理头显姿态数据并发送到云台舵机
            if 'head_mat' in result and result['head_mat'] is not None:
                # print(f"🎯 Head matrix available, shape: {result['head_mat'].shape}")

                # 检查head_mat是否为零矩阵或单位矩阵
                head_mat_sum = np.sum(np.abs(result['head_mat']))
                is_identity = np.allclose(result['head_mat'], np.eye(4))
                # print(f"📊 Head matrix sum: {head_mat_sum:.3f}, is_identity: {is_identity}")

                if head_mat_sum > 0.1 and not is_identity:  # 确保不是零矩阵或单位矩阵
                    # print(f"📊 Head matrix (first 2 rows):\n{result['head_mat'][:2]}")

                    ypr, servo_angles = GimbalController.process_head_to_servo_angles(result['head_mat'])
                    # print(f"🔄 Processed angles - ypr: {ypr}, servo_angles: {servo_angles}")

                    if ypr is not None and servo_angles is not None:
                        # Convert to degrees
                        yaw_deg = np.degrees(servo_angles[0])
                        pitch_deg = np.degrees(servo_angles[1])
                        # print(f"📐 Converted to degrees - yaw: {yaw_deg:.2f}°, pitch: {pitch_deg:.2f}°")

                        # Send to gimbal servos using ROS2 service
                        success = hand_controller.send_to_gimbal(yaw_deg, pitch_deg)
                        # print(f"📡 Gimbal send result: {success}")
                    else:
                        # print("❌ ypr or servo_angles is None")
                        pass
                else:
                    # print(f"⚠️  Head matrix appears to be zero or identity matrix")
                    pass
            else:
                # print(f"❌ No head_mat in result or head_mat is None. Keys: {result.keys()}")
                pass
            
            # Send commands (every frame for maximum responsiveness)
            if result['valid']:
                hand_controller.send_wrist_pose(result['left_wrist_mat'], result['right_wrist_mat'])
                # 1. Update hands (with throttling to reduce ROS2 traffic)
                angle_threshold = 5.0  # 角度变化阈值 (度) - 降低因为现在是角度值而不是0-1000
                
                send_left = True
                send_right = True
                
                if last_angles_left is not None:
                    left_diff = np.abs(np.array(result['left_angles']) - np.array(last_angles_left))
                    send_left = np.any(left_diff > angle_threshold)
                
                if last_angles_right is not None:
                    right_diff = np.abs(np.array(result['right_angles']) - np.array(last_angles_right))
                    send_right = np.any(right_diff > angle_threshold)
                
                if send_left or send_right:
                    # Print angles before sending
                    if send_right:
                        print(f"\n🎯 Sending Right Hand Commands:")
                        print(f"   📐 Raw angles: {result['right_angles']}")
                    
                    success = hand_controller.send_hand_commands(result['left_angles'], result['right_angles'])
                    if success:
                        last_angles_left = result['left_angles'].copy()
                        last_angles_right = result['right_angles'].copy()
            
            hand_controller.is_pub_start_flag(result['left_angles'], result['right_angles'])
            # Process ROS2 callbacks for hand controller
            # rclpy.spin_once(hand_controller, timeout_sec=0.001)
            
            # Small sleep to prevent excessive CPU usage (but prioritize arm control)
            time.sleep(1.0/60.0)  # 60 FPS
            
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        teleoperator.cleanup()
        hand_controller.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main() 
